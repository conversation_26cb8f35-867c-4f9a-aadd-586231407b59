<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AL-SALAMAT - شركة زجاج السيارات</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="realtime-styles.css">

    <!-- Firebase CDN for contact form -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <!-- Homepage Authentication Manager -->
    <script src="homepage-auth.js"></script>

    <!-- Dynamic Content Manager -->
    <script src="dynamic-content.js"></script>

    <!-- Gallery Manager -->
    <script src="gallery-manager.js"></script>

    <!-- Image Modal Manager -->
    <script src="image-modal.js"></script>
</head>
<body>


    <header>
        <nav class="container">
            <div class="logo">AL-SALAMAT</div>
            <button class="mobile-menu-btn" onclick="toggleSidebar()">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <ul class="nav-links desktop-nav">
                <li>
                    <button class="language-toggle" onclick="toggleLanguage()" id="language-toggle">
                        <span class="lang-icon">🌐</span>
                        <span class="lang-text" id="lang-text">EN</span>
                    </button>
                </li>
                <li id="login-menu-item">
                    <a href="login.html" class="login-link" data-ar="التسجيل" data-en="Login">التسجيل</a>
                    <div class="user-menu" id="user-menu" style="display: none;">
                        <button class="user-menu-btn" onclick="toggleUserDropdown()" data-ar="تم التسجيل ▼" data-en="Logged In ▼">تم التسجيل ▼</button>
                        <div class="user-dropdown" id="user-dropdown">
                            <div class="user-dropdown-item status" data-ar="تم التسجيل" data-en="Logged In">تم التسجيل</div>
                            <div class="user-dropdown-item admin-link" id="admin-link" style="display: none;" onclick="window.location.href='admin.html'" data-ar="لوحة الإدارة" data-en="Admin Panel">لوحة الإدارة</div>
                            <div class="user-dropdown-item logout" onclick="logout()" data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</div>
                        </div>
                    </div>
                </li>
                <li><a href="#home" data-ar="الرئيسية" data-en="Home">الرئيسية</a></li>
                <li><a href="#about" data-ar="من نحن" data-en="About Us">من نحن</a></li>
                <li><a href="#gallery" data-ar="معرض الصور" data-en="Gallery">معرض الصور</a></li>
                <li><a href="#branches" data-ar="الفروع" data-en="Branches">الفروع</a></li>
                <li><a href="#contact" data-ar="اتصل بنا" data-en="Contact Us">اتصل بنا</a></li>
            </ul>
        </nav>
    </header>

    <!-- Mobile Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">AL-SALAMAT</div>
            <button class="close-btn" onclick="toggleSidebar()">&times;</button>
        </div>
        <div id="sidebar-login-section">
            <a href="login.html" class="sidebar-login-link" data-ar="التسجيل" data-en="Login">التسجيل</a>
        </div>
        <div class="sidebar-user-menu" id="sidebar-user-menu" style="display: none;">
            <div class="sidebar-user-status" data-ar="تم التسجيل" data-en="Logged In">تم التسجيل</div>
            <div class="sidebar-logout-btn" onclick="logout()" data-ar="تسجيل الخروج" data-en="Logout">تسجيل الخروج</div>
        </div>
        <ul class="sidebar-nav">
            <li>
                <button class="sidebar-language-toggle" onclick="toggleLanguage()">
                    <span class="lang-icon">🌐</span>
                    <span class="lang-text" data-ar="English" data-en="العربية">English</span>
                </button>
            </li>
            <li><a href="#home" onclick="toggleSidebar()" data-ar="الرئيسية" data-en="Home">الرئيسية</a></li>
            <li><a href="#about" onclick="toggleSidebar()" data-ar="من نحن" data-en="About Us">من نحن</a></li>
            <li><a href="#gallery" onclick="toggleSidebar()" data-ar="معرض الصور" data-en="Gallery">معرض الصور</a></li>
            <li><a href="#branches" onclick="toggleSidebar()" data-ar="الفروع" data-en="Branches">الفروع</a></li>
            <li><a href="#contact" onclick="toggleSidebar()" data-ar="اتصل بنا" data-en="Contact Us">اتصل بنا</a></li>
        </ul>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay" onclick="toggleSidebar()"></div>

    <!-- Image Modal -->
    <div class="image-modal" id="imageModal" onclick="closeImageModal()">
        <div class="image-modal-content" onclick="event.stopPropagation()">
            <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
            <img class="image-modal-img" id="modalImage" src="" alt="">
            <div class="image-modal-info">
                <h3 id="modalTitle"></h3>
                <p id="modalDescription"></p>
            </div>
        </div>
    </div>



    <main class="container">
        <section id="home" class="hero-section">
            <div class="hero-title-container">
                <img src="img/logo2.png" alt="لوجو شركة السلامات" id="company-logo" class="company-logo-image">
                <h1 class="hero-title" id="company-title" data-ar="السلامات لزجاج السيارات" data-en="AL-SALAMAT Car Glass">السلامات لزجاج السيارات</h1>
            </div>
            <h2 class="hero-subtitle" id="company-subtitle" data-ar="رائدة في زجاج السيارات" data-en="Leading in Car Glass Services">رائدة في زجاج السيارات</h2>
            <div class="company-image-container">
                <img src="img/main1.jpg" alt="زجاج السيارات" class="company-image">
                <div class="image-overlay-top"></div>
                <div class="image-overlay-bottom"></div>
            </div>

            <!-- Moving Text Banner -->
            <section class="moving-text-banner">
                <div class="moving-text-container">
                    <div class="moving-text">
                        <span>السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات</span>
                        <span>السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات</span>
                        <span>السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات</span>
                        <span>السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات</span>
                        <span>السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات</span>
                    </div>
                </div>
            </section>

            <!-- Gallery Images -->
            <div class="gallery-section">
                <div class="gallery-grid" id="dynamic-gallery">
                    <!-- سيتم تحميل الصور من Firebase -->
                    <div class="gallery-item" data-title="خدمات زجاج السيارات المتميزة">
                        <img src="img/car2.png" alt="خدمات زجاج السيارات" class="gallery-image">
                        <div class="gallery-item-overlay">
                            <h3>خدمات زجاج السيارات</h3>
                            <p>أفضل خدمات تركيب وإصلاح زجاج السيارات</p>
                        </div>
                    </div>
                    <div class="gallery-item" data-title="تركيب زجاج احترافي">
                        <img src="img/car4.png" alt="تركيب زجاج السيارات" class="gallery-image">
                        <div class="gallery-item-overlay">
                            <h3>تركيب زجاج السيارات</h3>
                            <p>تركيب احترافي بأعلى معايير الجودة</p>
                        </div>
                    </div>
                    <div class="gallery-item" data-title="إصلاح سريع ومضمون">
                        <img src="img/car6.png" alt="إصلاح زجاج السيارات" class="gallery-image">
                        <div class="gallery-item-overlay">
                            <h3>إصلاح زجاج السيارات</h3>
                            <p>إصلاح سريع وفعال لجميع أنواع السيارات</p>
                        </div>
                    </div>

                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about-section">
            <h2 class="about-title" id="about-title" data-ar="من نحن" data-en="About Us">من نحن</h2>
            <div class="about-content">
                <div class="about-loading" id="about-loading">
                    <div class="loading-spinner"></div>
                    <p data-ar="جاري تحميل المحتوى..." data-en="Loading content...">جاري تحميل المحتوى...</p>
                </div>
                <p class="about-description hidden" id="about-description"></p>
            </div>
        </section>

        <!-- Gallery Section -->
        <section id="gallery" class="gallery-main-section">
            <h2 class="gallery-main-title" data-ar="معرض الصور" data-en="Photo Gallery">معرض الصور</h2>
            <div class="gallery-container">
                <!-- Gallery Content -->
                <div class="gallery-content">
                    <div class="gallery-header">
                        <h3 class="current-category-title">جميع الصور</h3>
                        <div class="gallery-controls">
                            <div class="gallery-pagination">
                                <span class="page-info">الصفحة <span id="current-page">1</span> من <span id="total-pages">2</span></span>
                            </div>
                        </div>
                    </div>

                    <div class="gallery-main-grid" id="main-gallery-grid">
                        <!-- سيتم تحميل الصور من Firebase -->
                        <div class="gallery-loading" id="gallery-loading">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل الصور...</p>
                        </div>

                        <!-- الصور ستظهر هنا ديناميكياً من Firebase -->
                    </div>

                    <!-- Pagination Controls -->
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prev-page-btn" onclick="changePage(-1)">
                            <span>‹</span>
                            <span>السابق</span>
                        </button>
                        <div class="pagination-numbers">
                            <button class="page-number active" data-page="1">1</button>
                            <button class="page-number" data-page="2">2</button>
                        </div>
                        <button class="pagination-btn" id="next-page-btn" onclick="changePage(1)">
                            <span>التالي</span>
                            <span>›</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <section id="branches" class="branches-section">
            <h2 class="branches-title" data-ar="فروعنا" data-en="Our Branches">فروعنا</h2>
            <div class="branches-grid" id="dynamic-branches">
                <!-- سيتم تحميل الفروع من Firebase -->
                <div class="no-data-message" id="no-branches-message">
                    <p data-ar="لا توجد فروع حالياً" data-en="No branches available">لا توجد فروع حالياً</p>
                </div>
            </div>
        </section>

        <!-- Separator -->
        <div class="section-separator"></div>

        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <h2 class="contact-title" id="contact-title" data-ar="اتصل بنا" data-en="Contact Us">اتصل بنا</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3 id="contact-info-title" data-ar="معلومات التواصل" data-en="Contact Information">معلومات التواصل</h3>
                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div class="contact-details">
                            <strong data-ar="الهاتف:" data-en="Phone:">الهاتف:</strong>
                            <span id="contact-phone-display">######</span>
                            <a href="tel:######" id="contact-phone-link" class="contact-action-btn" data-ar="اتصل الآن" data-en="Call Now">اتصل الآن</a>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <strong data-ar="البريد الإلكتروني:" data-en="Email:">البريد الإلكتروني:</strong>
                            <span id="contact-email-display">######</span>
                            <a href="mailto:######" id="contact-email-link" class="contact-action-btn" data-ar="إرسال بريد" data-en="Send Email">إرسال بريد</a>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <strong data-ar="العنوان:" data-en="Address:">العنوان:</strong>
                            <span id="contact-address-display">######</span>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">🕒</div>
                        <div class="contact-details">
                            <strong data-ar="ساعات العمل:" data-en="Working Hours:">ساعات العمل:</strong>
                            <span id="contact-hours-display">######</span>
                        </div>
                    </div>
                </div>

                <div class="contact-form-container">
                    <h3 data-ar="أرسل لنا رسالة" data-en="Send us a Message">أرسل لنا رسالة</h3>
                    <form id="contact-form" class="contact-form">
                        <div class="form-group">
                            <label for="contact-name" data-ar="الاسم" data-en="Name">الاسم</label>
                            <input type="text" id="contact-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-email" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
                            <input type="email" id="contact-email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-phone" data-ar="رقم الهاتف" data-en="Phone Number">رقم الهاتف</label>
                            <input type="tel" id="contact-phone" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-message" data-ar="الرسالة" data-en="Message">الرسالة</label>
                            <textarea id="contact-message" name="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="contact-submit-btn" data-ar="إرسال الرسالة" data-en="Send Message">إرسال الرسالة</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p data-ar="&copy; 2024 شركة AL-SALAMAT لزجاج السيارات. جميع الحقوق محفوظة." data-en="&copy; 2024 AL-SALAMAT Car Glass Company. All rights reserved.">&copy; 2024 شركة AL-SALAMAT لزجاج السيارات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        console.log('Firebase initialized successfully');

        // Load content immediately without authentication
        function loadContentDirectly() {
            console.log('🚀 Loading content directly from Firebase...');

            const database = firebase.database();

            // Load branches
            database.ref('branches').once('value').then((snapshot) => {
                const data = snapshot.val();
                console.log('📥 Branches loaded:', data);

                const branchesGrid = document.getElementById('dynamic-branches');
                if (branchesGrid && data) {
                    branchesGrid.innerHTML = '';
                    Object.entries(data).forEach(([id, branch]) => {
                        const branchCard = document.createElement('div');
                        branchCard.className = 'branch-card';
                        branchCard.innerHTML = `
                            <h3>${branch.name || 'فرع غير محدد'}</h3>
                            <p><strong>العنوان:</strong> ${branch.address || 'عنوان غير محدد'}</p>
                            ${branch.phone ? `<p><strong>الهاتف:</strong> ${branch.phone}</p>` : ''}
                            <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || branch.name)}"
                               target="_blank" class="location-btn">📍 الموقع</a>
                        `;
                        branchesGrid.appendChild(branchCard);
                    });
                    console.log('✅ Branches updated');
                }
            }).catch(error => {
                console.error('❌ Error loading branches:', error);
                console.error('Error details:', error.message);
                console.error('Error code:', error.code);
            });

            // Load about section
            database.ref('aboutSection').once('value').then((snapshot) => {
                const data = snapshot.val();
                console.log('📥 About section loaded:', data);

                if (data) {
                    const aboutLoading = document.getElementById('about-loading');
                    const aboutDescription = document.getElementById('about-description');

                    if (aboutLoading) aboutLoading.style.display = 'none';
                    if (aboutDescription && data.description) {
                        aboutDescription.textContent = data.description;
                        aboutDescription.classList.remove('hidden');
                    }
                    console.log('✅ About section updated');
                }
            }).catch(error => {
                console.error('❌ Error loading about section:', error);
                console.error('Error details:', error.message);
            });

            // Load contact section
            database.ref('contactSection').once('value').then((snapshot) => {
                const data = snapshot.val();
                console.log('📥 Contact section loaded:', data);

                if (data) {
                    if (data.address) {
                        const addressElement = document.getElementById('contact-address-display');
                        if (addressElement) addressElement.textContent = data.address;
                    }
                    if (data.hours) {
                        const hoursElement = document.getElementById('contact-hours-display');
                        if (hoursElement) hoursElement.textContent = data.hours;
                    }
                    console.log('✅ Contact section updated');
                }
            }).catch(error => console.error('Error loading contact section:', error));

            // Load site settings
            database.ref('siteSettings').once('value').then((snapshot) => {
                const data = snapshot.val();
                console.log('📥 Site settings loaded:', data);

                if (data) {
                    if (data.contactEmail) {
                        const emailElement = document.getElementById('contact-email-display');
                        const emailLink = document.getElementById('contact-email-link');
                        if (emailElement) emailElement.textContent = data.contactEmail;
                        if (emailLink) emailLink.href = `mailto:${data.contactEmail}`;
                    }
                    if (data.contactPhone) {
                        const phoneElement = document.getElementById('contact-phone-display');
                        const phoneLink = document.getElementById('contact-phone-link');
                        if (phoneElement) phoneElement.textContent = data.contactPhone;
                        if (phoneLink) phoneLink.href = `tel:${data.contactPhone}`;
                    }
                    console.log('✅ Site settings updated');
                }
            }).catch(error => console.error('Error loading site settings:', error));

            // Hide gallery loading
            const galleryLoading = document.getElementById('gallery-loading');
            if (galleryLoading) galleryLoading.style.display = 'none';

            console.log('✅ Direct content loading completed');

            // Set up real-time listeners for automatic updates
            setupRealtimeListeners(database);
        }

        // Set up real-time listeners
        function setupRealtimeListeners(database) {
            console.log('🔄 Setting up real-time listeners...');

            // Listen for branches changes
            database.ref('branches').on('value', (snapshot) => {
                const data = snapshot.val();
                const branchesGrid = document.getElementById('dynamic-branches');
                if (branchesGrid && data) {
                    branchesGrid.innerHTML = '';
                    Object.entries(data).forEach(([id, branch]) => {
                        const branchCard = document.createElement('div');
                        branchCard.className = 'branch-card';
                        branchCard.innerHTML = `
                            <h3>${branch.name || 'فرع غير محدد'}</h3>
                            <p><strong>العنوان:</strong> ${branch.address || 'عنوان غير محدد'}</p>
                            ${branch.phone ? `<p><strong>الهاتف:</strong> ${branch.phone}</p>` : ''}
                            <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || branch.name)}"
                               target="_blank" class="location-btn">📍 الموقع</a>
                        `;
                        branchesGrid.appendChild(branchCard);
                    });
                }
            });

            // Listen for about section changes
            database.ref('aboutSection').on('value', (snapshot) => {
                const data = snapshot.val();
                if (data) {
                    const aboutLoading = document.getElementById('about-loading');
                    const aboutDescription = document.getElementById('about-description');

                    if (aboutLoading) aboutLoading.style.display = 'none';
                    if (aboutDescription && data.description) {
                        aboutDescription.textContent = data.description;
                        aboutDescription.classList.remove('hidden');
                    }
                }
            });

            // Listen for site settings changes
            database.ref('siteSettings').on('value', (snapshot) => {
                const data = snapshot.val();
                if (data) {
                    if (data.contactEmail) {
                        const emailElement = document.getElementById('contact-email-display');
                        const emailLink = document.getElementById('contact-email-link');
                        if (emailElement) emailElement.textContent = data.contactEmail;
                        if (emailLink) emailLink.href = `mailto:${data.contactEmail}`;
                    }
                    if (data.contactPhone) {
                        const phoneElement = document.getElementById('contact-phone-display');
                        const phoneLink = document.getElementById('contact-phone-link');
                        if (phoneElement) phoneElement.textContent = data.contactPhone;
                        if (phoneLink) phoneLink.href = `tel:${data.contactPhone}`;
                    }
                }
            });

            console.log('✅ Real-time listeners set up');
        }

        // Load content immediately when Firebase is ready
        setTimeout(loadContentDirectly, 500);

        // Also try loading content multiple times to ensure it works
        setTimeout(loadContentDirectly, 1000);
        setTimeout(loadContentDirectly, 2000);

        // Load logo
        loadSiteLogo();

        // Language Management
        let currentLanguage = localStorage.getItem('language') || 'ar';

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            localStorage.setItem('language', currentLanguage);
            updateLanguage();
        }

        function updateLanguage() {
            const elements = document.querySelectorAll('[data-ar][data-en]');
            const langToggleText = document.getElementById('lang-text');
            const sidebarLangText = document.querySelector('.sidebar-language-toggle .lang-text');

            elements.forEach(element => {
                if (currentLanguage === 'ar') {
                    element.textContent = element.getAttribute('data-ar');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });

            // Update language toggle button text
            if (langToggleText) {
                langToggleText.textContent = currentLanguage === 'ar' ? 'EN' : 'ع';
            }

            if (sidebarLangText) {
                sidebarLangText.textContent = currentLanguage === 'ar' ? 'English' : 'العربية';
            }

            // Update page direction
            document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.lang = currentLanguage;

            // Update body class for styling
            document.body.className = document.body.className.replace(/lang-\w+/, '');
            document.body.classList.add(`lang-${currentLanguage}`);

            console.log(`Language switched to: ${currentLanguage}`);
        }

        // Load custom texts from Firebase
        function loadCustomTexts() {
            console.log('🌐 Loading custom texts from Firebase...');

            const database = firebase.database();

            // Load main texts
            database.ref('siteTexts/main').once('value').then((snapshot) => {
                const data = snapshot.val();
                if (data) {
                    // Update main title
                    const titleElement = document.getElementById('company-title');
                    if (titleElement) {
                        titleElement.setAttribute('data-ar', data.companyTitleAr || 'السلامات لزجاج السيارات');
                        titleElement.setAttribute('data-en', data.companyTitleEn || 'AL-SALAMAT Car Glass');
                    }

                    // Update subtitle
                    const subtitleElement = document.getElementById('company-subtitle');
                    if (subtitleElement) {
                        subtitleElement.setAttribute('data-ar', data.companySubtitleAr || 'رائدة في زجاج السيارات');
                        subtitleElement.setAttribute('data-en', data.companySubtitleEn || 'Leading in Car Glass Services');
                    }

                    console.log('✅ Main texts loaded');
                    updateLanguage(); // Apply current language
                }
            }).catch(error => console.error('Error loading main texts:', error));

            // Load menu texts
            database.ref('siteTexts/menu').once('value').then((snapshot) => {
                const data = snapshot.val();
                if (data) {
                    // Update navigation links
                    const navLinks = {
                        'home': { ar: data.homeAr || 'الرئيسية', en: data.homeEn || 'Home' },
                        'about': { ar: data.aboutAr || 'من نحن', en: data.aboutEn || 'About Us' },
                        'gallery': { ar: data.galleryAr || 'معرض الصور', en: data.galleryEn || 'Gallery' },
                        'branches': { ar: data.branchesAr || 'الفروع', en: data.branchesEn || 'Branches' },
                        'contact': { ar: data.contactAr || 'اتصل بنا', en: data.contactEn || 'Contact Us' }
                    };

                    Object.keys(navLinks).forEach(key => {
                        const elements = document.querySelectorAll(`[href="#${key}"]`);
                        elements.forEach(element => {
                            element.setAttribute('data-ar', navLinks[key].ar);
                            element.setAttribute('data-en', navLinks[key].en);
                        });
                    });

                    console.log('✅ Menu texts loaded');
                    updateLanguage(); // Apply current language
                }
            }).catch(error => console.error('Error loading menu texts:', error));

            // Load contact texts
            database.ref('siteTexts/contact').once('value').then((snapshot) => {
                const data = snapshot.val();
                if (data) {
                    // Update contact section title
                    const contactTitle = document.getElementById('contact-title');
                    if (contactTitle) {
                        contactTitle.setAttribute('data-ar', data.titleAr || 'اتصل بنا');
                        contactTitle.setAttribute('data-en', data.titleEn || 'Contact Us');
                    }

                    // Update contact info title
                    const contactInfoTitle = document.getElementById('contact-info-title');
                    if (contactInfoTitle) {
                        contactInfoTitle.setAttribute('data-ar', data.infoTitleAr || 'معلومات التواصل');
                        contactInfoTitle.setAttribute('data-en', data.infoTitleEn || 'Contact Information');
                    }

                    console.log('✅ Contact texts loaded');
                    updateLanguage(); // Apply current language
                }
            }).catch(error => console.error('Error loading contact texts:', error));

            // Load sections texts
            database.ref('siteTexts/sections').once('value').then((snapshot) => {
                const data = snapshot.val();
                if (data) {
                    // Update about section title
                    const aboutTitle = document.getElementById('about-title');
                    if (aboutTitle) {
                        aboutTitle.setAttribute('data-ar', data.aboutAr || 'من نحن');
                        aboutTitle.setAttribute('data-en', data.aboutEn || 'About Us');
                    }

                    // Update gallery section title
                    const galleryTitle = document.querySelector('.gallery-main-title');
                    if (galleryTitle) {
                        galleryTitle.setAttribute('data-ar', data.galleryAr || 'معرض الصور');
                        galleryTitle.setAttribute('data-en', data.galleryEn || 'Photo Gallery');
                    }

                    // Update branches section title
                    const branchesTitle = document.querySelector('.branches-title');
                    if (branchesTitle) {
                        branchesTitle.setAttribute('data-ar', data.branchesAr || 'فروعنا');
                        branchesTitle.setAttribute('data-en', data.branchesEn || 'Our Branches');
                    }

                    console.log('✅ Sections texts loaded');
                    updateLanguage(); // Apply current language
                }
            }).catch(error => console.error('Error loading sections texts:', error));

            // Set up real-time listeners for text updates
            database.ref('siteTexts').on('value', (snapshot) => {
                console.log('🔄 Texts updated in real-time');
                setTimeout(() => {
                    loadCustomTexts();
                }, 100);
            });
        }

        // Initialize language on page load
        function initializeLanguage() {
            updateLanguage();
        }

        // Initialize language
        setTimeout(initializeLanguage, 100);

        // Load custom texts
        setTimeout(loadCustomTexts, 500);

        // Listen for text updates from admin panel
        window.addEventListener('storage', (e) => {
            if (e.key === 'textsUpdate') {
                console.log('🔄 Texts updated from admin panel');
                setTimeout(loadCustomTexts, 200);
            }
        });

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        }

        // Close sidebar when clicking on a link
        document.querySelectorAll('.sidebar-nav a').forEach(link => {
            link.addEventListener('click', () => {
                toggleSidebar();
            });
        });

        // Toggle user dropdown menu
        function toggleUserDropdown() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-menu');
            const dropdown = document.getElementById('user-dropdown');

            if (userMenu && !userMenu.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Check if user is logged in and update login button
        function checkLoginStatus() {
            const user = localStorage.getItem('user');

            if (user) {
                const userData = JSON.parse(user);
                const userName = userData.displayName || 'المستخدم';

                // Show user menu in desktop nav
                document.querySelector('.login-link').style.display = 'none';
                document.getElementById('user-menu').style.display = 'inline-block';

                // Show user menu in sidebar
                document.getElementById('sidebar-login-section').style.display = 'none';
                document.getElementById('sidebar-user-menu').style.display = 'block';

                // Check if user is admin and show admin link
                checkAdminStatus(userData);
            } else {
                // Show login links
                document.querySelector('.login-link').style.display = 'inline-block';
                document.getElementById('user-menu').style.display = 'none';

                // Show login link in sidebar
                document.getElementById('sidebar-login-section').style.display = 'block';
                document.getElementById('sidebar-user-menu').style.display = 'none';
            }
        }

        // Check if user has admin privileges
        async function checkAdminStatus(userData) {
            try {
                // Admin emails with full access
                const adminEmails = ['<EMAIL>']; // Main admin email

                if (adminEmails.includes(userData.email) || userData.displayName === 'admin') {
                    document.getElementById('admin-link').style.display = 'block';
                }
            } catch (error) {
                console.error('Error checking admin status:', error);
            }
        }

        // Logout function
        function logout() {
            if (confirm('هل تريد تسجيل الخروج؟')) {
                localStorage.removeItem('user');

                // Hide user menus and show login links
                document.querySelector('.login-link').style.display = 'inline-block';
                document.getElementById('user-menu').style.display = 'none';

                // Hide sidebar user menu and show login link
                document.getElementById('sidebar-login-section').style.display = 'block';
                document.getElementById('sidebar-user-menu').style.display = 'none';

                // Close dropdown if open
                document.getElementById('user-dropdown').classList.remove('show');

                alert('تم تسجيل الخروج بنجاح');
            }
        }

        // Handle contact form submission
        document.getElementById('contact-form').addEventListener('submit', async function(event) {
            event.preventDefault();

            const name = document.getElementById('contact-name').value;
            const email = document.getElementById('contact-email').value;
            const phone = document.getElementById('contact-phone').value;
            const message = document.getElementById('contact-message').value;

            const submitBtn = document.querySelector('.contact-submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'جاري الإرسال...';
            submitBtn.disabled = true;

            try {
                // Save message to Firebase (if Firebase is available)
                if (typeof firebase !== 'undefined') {
                    const database = firebase.database();
                    await database.ref('contactForms').push({
                        name: name,
                        email: email,
                        phone: phone,
                        message: message,
                        submittedAt: new Date().toISOString(),
                        status: 'new',
                        userAgent: navigator.userAgent
                    });
                }

                alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
                document.getElementById('contact-form').reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            } catch (error) {
                console.error('Error sending message:', error);
                alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
                document.getElementById('contact-form').reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // All content updates are now handled by dynamic-content.js

        // Gallery Management Functions
        let currentPage = 1;
        const itemsPerPage = 2;

        function initializeGallery() {
            const galleryItems = document.querySelectorAll('.gallery-main-item');
            const currentCategoryTitle = document.querySelector('.current-category-title');
            const galleryGrid = document.getElementById('main-gallery-grid');

            // Update counts (simplified without sidebar)
            function updateCounts() {
                // No longer needed since sidebar is removed
            }

            // Show gallery items with pagination (no filtering)
            function showGalleryPage() {
                const allItems = document.querySelectorAll('.gallery-main-item');

                // Hide all items first
                allItems.forEach(item => item.style.display = 'none');

                // Calculate pagination
                const totalPages = Math.ceil(allItems.length / itemsPerPage);
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = startIndex + itemsPerPage;

                // Show items for current page
                Array.from(allItems).slice(startIndex, endIndex).forEach((item, index) => {
                    item.style.display = 'block';
                    item.style.animation = `fadeInUp 0.5s ease-out ${index * 0.1}s both`;
                });

                updatePagination(totalPages, allItems.length);
            }

            // Update pagination controls
            function updatePagination(totalPages, totalItems) {
                document.getElementById('current-page').textContent = currentPage;
                document.getElementById('total-pages').textContent = totalPages;

                // Update pagination buttons
                const prevBtn = document.getElementById('prev-page-btn');
                const nextBtn = document.getElementById('next-page-btn');

                prevBtn.disabled = currentPage === 1;
                nextBtn.disabled = currentPage === totalPages;

                // Update page numbers
                const pageNumbers = document.querySelectorAll('.page-number');
                pageNumbers.forEach(btn => {
                    btn.classList.remove('active');
                    if (parseInt(btn.dataset.page) === currentPage) {
                        btn.classList.add('active');
                    }
                });
            }

            // Page number click handlers
            document.querySelectorAll('.page-number').forEach(btn => {
                btn.addEventListener('click', function() {
                    currentPage = parseInt(this.dataset.page);
                    showGalleryPage();
                });
            });

            // Initialize gallery page
            showGalleryPage();

            // Hide loading
            const galleryLoading = document.getElementById('gallery-loading');
            if (galleryLoading) {
                galleryLoading.style.display = 'none';
            }
        }

        // Page navigation function
        function changePage(direction) {
            const allItems = document.querySelectorAll('.gallery-main-item');
            const totalPages = Math.ceil(allItems.length / itemsPerPage);

            currentPage += direction;

            if (currentPage < 1) currentPage = 1;
            if (currentPage > totalPages) currentPage = totalPages;

            // Get the gallery instance and show current page
            const galleryInstance = window.galleryManager || { showGalleryPage: () => {} };
            if (typeof galleryInstance.showGalleryPage === 'function') {
                galleryInstance.showGalleryPage();
            } else {
                // Fallback: call the function directly
                const galleryItems = document.querySelectorAll('.gallery-main-item');
                galleryItems.forEach(item => item.style.display = 'none');

                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = startIndex + itemsPerPage;

                Array.from(galleryItems).slice(startIndex, endIndex).forEach((item, index) => {
                    item.style.display = 'block';
                    item.style.animation = `fadeInUp 0.5s ease-out ${index * 0.1}s both`;
                });

                // Update pagination display
                document.getElementById('current-page').textContent = currentPage;
                document.getElementById('total-pages').textContent = totalPages;

                const prevBtn = document.getElementById('prev-page-btn');
                const nextBtn = document.getElementById('next-page-btn');

                prevBtn.disabled = currentPage === 1;
                nextBtn.disabled = currentPage === totalPages;

                const pageNumbers = document.querySelectorAll('.page-number');
                pageNumbers.forEach(btn => {
                    btn.classList.remove('active');
                    if (parseInt(btn.dataset.page) === currentPage) {
                        btn.classList.add('active');
                    }
                });
            }
        }

        // Image Modal Functions
        function openImageModal(imageSrc, title, description) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');
            const modalDescription = document.getElementById('modalDescription');

            modalImage.src = imageSrc;
            modalImage.alt = title;
            modalTitle.textContent = title;
            modalDescription.textContent = description;

            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Add animation
            setTimeout(() => {
                modal.classList.add('active');
            }, 10);
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('active');

            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });

        // Ensure banner is always visible
        function ensureBannerVisible() {
            const banner = document.querySelector('.moving-text-banner');
            if (banner) {
                banner.style.display = 'flex';
                banner.style.visibility = 'visible';
                banner.style.opacity = '1';
                banner.classList.remove('hidden');

                // Also ensure text elements are visible
                const textElements = banner.querySelectorAll('.moving-text span');
                textElements.forEach(span => {
                    span.style.visibility = 'visible';
                    span.style.opacity = '1';
                });
            }
        }

        // Contact links management
        function initializeContactLinks() {
            console.log('🔗 Initializing contact links...');

            // First, set default disabled links
            setDefaultContactLinks();

            // Try to load contact data from Firebase
            if (typeof firebase !== 'undefined') {
                const database = firebase.database();

                // Load site settings for contact info (one-time)
                database.ref('siteSettings').once('value').then((snapshot) => {
                    const data = snapshot.val();
                    if (data && data.contactEmail && data.contactPhone &&
                        data.contactEmail !== '######' && data.contactPhone !== '######') {
                        console.log('📊 Real data found in Firebase, enabling links:', data);
                        updateContactLinks(data);
                    } else {
                        console.log('⚠️ No complete real data found in Firebase, keeping links disabled');
                        setDefaultContactLinks();
                    }
                }).catch((error) => {
                    console.error('Error loading contact data:', error);
                    setDefaultContactLinks();
                });

                // Set up real-time listener for updates
                database.ref('siteSettings').on('value', (snapshot) => {
                    const data = snapshot.val();
                    if (data && data.contactEmail && data.contactPhone &&
                        data.contactEmail !== '######' && data.contactPhone !== '######') {
                        console.log('🔄 Real data detected, enabling contact links:', data);
                        updateContactLinks(data);
                    } else {
                        console.log('⚠️ Incomplete or placeholder data, keeping links disabled');
                        setDefaultContactLinks();
                    }
                });
            } else {
                setDefaultContactLinks();
            }
        }

        function updateContactLinks(data) {
            console.log('📞 Updating contact links with real data:', data);

            const emailLink = document.getElementById('contact-email-link');
            const phoneLink = document.getElementById('contact-phone-link');
            const emailDisplay = document.getElementById('contact-email-display');
            const phoneDisplay = document.getElementById('contact-phone-display');

            if (data.contactEmail && emailLink && emailDisplay) {
                emailDisplay.textContent = data.contactEmail;
                emailLink.href = `mailto:${data.contactEmail}`;
                emailLink.onclick = function(e) {
                    console.log('📧 Opening email client for:', data.contactEmail);
                    window.location.href = `mailto:${data.contactEmail}`;
                };
                console.log('✅ Email link updated:', data.contactEmail);
            }

            if (data.contactPhone && phoneLink && phoneDisplay) {
                phoneDisplay.textContent = data.contactPhone;
                phoneLink.href = `tel:${data.contactPhone}`;
                phoneLink.onclick = function(e) {
                    console.log('📞 Opening phone dialer for:', data.contactPhone);
                    window.location.href = `tel:${data.contactPhone}`;
                };
                console.log('✅ Phone link updated:', data.contactPhone);
            }
        }

        function setDefaultContactLinks() {
            console.log('⚠️ Setting default contact links (disabled)');

            const emailLink = document.getElementById('contact-email-link');
            const phoneLink = document.getElementById('contact-phone-link');
            const emailDisplay = document.getElementById('contact-email-display');
            const phoneDisplay = document.getElementById('contact-phone-display');

            // Update display text
            if (emailDisplay) emailDisplay.textContent = '######';
            if (phoneDisplay) phoneDisplay.textContent = '######';

            if (emailLink) {
                emailLink.href = '#';
                emailLink.onclick = function(e) {
                    e.preventDefault();
                    alert('معلومات البريد الإلكتروني غير متاحة حالياً. يرجى المحاولة لاحقاً.');
                };
            }

            if (phoneLink) {
                phoneLink.href = '#';
                phoneLink.onclick = function(e) {
                    e.preventDefault();
                    alert('معلومات الهاتف غير متاحة حالياً. يرجى المحاولة لاحقاً.');
                };
            }
        }

        // Test function for contact links (for debugging)
        function testContactLinks() {
            const emailLink = document.getElementById('contact-email-link');
            const phoneLink = document.getElementById('contact-phone-link');

            console.log('📧 Email link:', emailLink ? emailLink.href : 'Not found');
            console.log('📞 Phone link:', phoneLink ? phoneLink.href : 'Not found');

            if (emailLink) {
                emailLink.style.border = '2px solid red';
                console.log('📧 Email onclick:', emailLink.onclick ? 'Set' : 'Not set');
            }
            if (phoneLink) {
                phoneLink.style.border = '2px solid blue';
                console.log('📞 Phone onclick:', phoneLink.onclick ? 'Set' : 'Not set');
            }

            setTimeout(() => {
                if (emailLink) emailLink.style.border = '';
                if (phoneLink) phoneLink.style.border = '';
            }, 3000);
        }

        // Force set working contact links (for immediate testing)
        function forceSetContactLinks() {
            console.log('🔧 Force setting contact links...');
            updateContactLinks({
                contactEmail: '<EMAIL>',
                contactPhone: '+966501234567'
            });
            console.log('✅ Contact links forced to working state');
        }

        // Simple test function to verify buttons work
        function testButtonsWork() {
            console.log('🧪 Testing contact buttons...');

            // Force enable buttons with test data
            forceSetContactLinks();

            // Add visual indicators
            setTimeout(() => {
                const emailLink = document.getElementById('contact-email-link');
                const phoneLink = document.getElementById('contact-phone-link');

                if (emailLink) {
                    emailLink.style.border = '3px solid green';
                    console.log('📧 Email button ready - try clicking it!');
                }
                if (phoneLink) {
                    phoneLink.style.border = '3px solid blue';
                    console.log('📞 Phone button ready - try clicking it!');
                }

                // Remove borders after 5 seconds
                setTimeout(() => {
                    if (emailLink) emailLink.style.border = '';
                    if (phoneLink) phoneLink.style.border = '';
                }, 5000);
            }, 500);
        }

        // Debug function to check data loading
        function debugDataLoading() {
            console.log('🔍 Debugging data loading...');

            // Check Firebase connection
            console.log('Firebase available:', typeof firebase !== 'undefined');

            if (typeof firebase !== 'undefined') {
                console.log('Firebase database available:', typeof firebase.database !== 'undefined');

                // Test direct Firebase connection
                const database = firebase.database();
                database.ref('siteSettings').once('value').then((snapshot) => {
                    const data = snapshot.val();
                    console.log('📊 Firebase data:', data);

                    if (data) {
                        console.log('📧 Email:', data.contactEmail);
                        console.log('📞 Phone:', data.contactPhone);

                        // Force update contact links
                        if (data.contactEmail && data.contactPhone) {
                            updateContactLinks(data);
                            console.log('✅ Contact links updated manually');
                        }
                    } else {
                        console.log('⚠️ No data found in Firebase');
                    }
                }).catch((error) => {
                    console.error('❌ Firebase error:', error);
                });
            }

            // Check connection manager
            if (window.connectionManager) {
                const status = window.connectionManager.getConnectionInfo();
                console.log('🔌 Connection status:', status);
            } else {
                console.log('⚠️ Connection manager not available');
            }
        }

        // Load site logo
        function loadSiteLogo() {
            console.log('🖼️ Loading site logo...');

            const logoElement = document.getElementById('company-logo');

            if (!logoElement) {
                console.error('❌ Logo element not found in DOM');
                return;
            }

            if (typeof firebase !== 'undefined') {
                const database = firebase.database();

                console.log('🔄 Fetching logo data from Firebase...');
                database.ref('siteLogo').once('value').then((snapshot) => {
                    const logoData = snapshot.val();
                    console.log('📊 Logo data received:', logoData);

                    if (logoData && logoData.url && logoData.enabled) {
                        console.log('✅ Logo found and enabled, displaying...');

                        // Update company logo
                        logoElement.src = logoData.url;
                        logoElement.alt = logoData.alt || 'لوجو شركة السلامات';
                        logoElement.style.display = 'block';

                        logoElement.onload = function() {
                            console.log('✅ Company logo image loaded successfully');
                        };
                        logoElement.onerror = function() {
                            console.error('❌ Error loading company logo image, using default');
                            logoElement.src = 'img/logo2.png';
                            logoElement.alt = 'لوجو شركة السلامات';
                        };

                        console.log('✅ Logo setup completed');
                    } else {
                        console.log('⚠️ No logo found or logo disabled, using default logo');
                        logoElement.src = 'img/logo2.png';
                        logoElement.alt = 'لوجو شركة السلامات';
                        logoElement.style.display = 'block';
                        logoContainer.style.display = 'block';
                    }
                }).catch((error) => {
                    console.error('❌ Error loading logo from Firebase:', error);
                    console.error('Error details:', error.message);
                    console.log('🔄 Using default logo as fallback');
                    logoElement.src = 'img/logo2.png';
                    logoElement.alt = 'لوجو شركة السلامات';
                    logoElement.style.display = 'block';
                });

                // Set up real-time listener for logo updates
                console.log('🔄 Setting up real-time logo listener...');
                database.ref('siteLogo').on('value', (snapshot) => {
                    const logoData = snapshot.val();
                    console.log('🔄 Real-time logo update received:', logoData);

                    if (logoData && logoData.url && logoData.enabled) {
                        // Update company logo
                        logoElement.src = logoData.url;
                        logoElement.alt = logoData.alt || 'لوجو شركة السلامات';
                        logoElement.style.display = 'block';

                        console.log('🔄 Logo updated in real-time');
                    } else {
                        console.log('🔄 Using default logo in real-time');
                        logoElement.src = 'img/logo2.png';
                        logoElement.alt = 'لوجو شركة السلامات';
                        logoElement.style.display = 'block';
                    }
                });
                console.log('✅ Real-time logo listener setup completed');
            } else {
                console.log('⚠️ Firebase not available, using default logo');
                logoElement.src = 'img/logo2.png';
                logoElement.alt = 'لوجو شركة السلامات';
                logoElement.style.display = 'block';
            }
        }

        // Test logo functionality
        function testLogo() {
            console.log('🧪 Testing logo functionality...');

            const logoElement = document.getElementById('company-logo');
            console.log('Logo element:', {
                logoElement: !!logoElement
            });

            if (typeof firebase !== 'undefined') {
                const database = firebase.database();
                console.log('Firebase database:', database);

                // Test reading logo data
                database.ref('siteLogo').once('value').then((snapshot) => {
                    const data = snapshot.val();
                    console.log('Logo data from Firebase:', data);

                    if (data) {
                        console.log('Logo URL length:', data.url ? data.url.length : 'No URL');
                        console.log('Logo enabled:', data.enabled);
                        console.log('Logo alt text:', data.alt);
                    }
                }).catch((error) => {
                    console.error('Error testing logo:', error);
                });
            } else {
                console.log('Firebase not available');
            }
        }

        // Force show logo for testing
        function forceShowLogo() {
            const logoElement = document.getElementById('company-logo');
            const testLogoSrc = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzY2N2VlYSIvPgo8dGV4dCB4PSIzMiIgeT0iMzgiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7YszwvdGV4dD4KPC9zdmc+';

            if (logoElement) {
                logoElement.style.display = 'block';
                logoElement.src = testLogoSrc;
                logoElement.alt = 'لوجو اختبار';
                console.log('✅ Test company logo displayed');
            }
        }

        // Make test functions available globally
        window.testContactLinks = testContactLinks;
        window.forceSetContactLinks = forceSetContactLinks;
        window.testButtonsWork = testButtonsWork;
        window.debugDataLoading = debugDataLoading;
        window.testLogo = testLogo;
        window.forceShowLogo = forceShowLogo;
        window.loadSiteLogo = loadSiteLogo;

        // Listen for logo updates from admin panel
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'LOGO_UPDATE') {
                console.log('📢 Received logo update from admin panel:', event.data.data);
                const logoData = event.data.data;
                const logoElement = document.getElementById('company-logo');

                if (logoElement && logoData && logoData.url && logoData.enabled) {
                    logoElement.src = logoData.url;
                    logoElement.alt = logoData.alt || 'لوجو شركة السلامات';
                    logoElement.style.display = 'block';

                    console.log('✅ Logo updated from admin panel message');
                } else if (logoElement) {
                    console.log('🔄 Using default logo from admin panel message');
                    logoElement.src = 'img/logo2.png';
                    logoElement.alt = 'لوجو شركة السلامات';
                    logoElement.style.display = 'block';
                }
            }
        });

        // Listen for localStorage changes (cross-tab communication)
        window.addEventListener('storage', function(event) {
            if (event.key === 'logoUpdate') {
                try {
                    const updateData = JSON.parse(event.newValue);
                    console.log('📢 Received logo update via localStorage:', updateData);

                    const logoData = updateData.data;
                    const logoElement = document.getElementById('company-logo');

                    if (logoElement && logoData && logoData.url && logoData.enabled) {
                        logoElement.src = logoData.url;
                        logoElement.alt = logoData.alt || 'لوجو شركة السلامات';
                        logoElement.style.display = 'block';

                        console.log('✅ Logo updated from localStorage');
                    } else if (logoElement) {
                        console.log('🔄 Using default logo from localStorage');
                        logoElement.src = 'img/logo2.png';
                        logoElement.alt = 'لوجو شركة السلامات';
                        logoElement.style.display = 'block';
                    }
                } catch (error) {
                    console.log('⚠️ Error parsing logo update from localStorage:', error);
                }
            }
        });

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            initializeGallery();
            ensureBannerVisible();
            initializeContactLinks(); // Initialize contact links
            loadSiteLogo(); // Load site logo
            console.log('📄 Page loaded, loading content directly...');

            // Load content immediately
            setTimeout(loadContentDirectly, 100);

            // Ensure banner stays visible
            setInterval(ensureBannerVisible, 2000);

            // Force check for branches after a delay to ensure everything is loaded
            setTimeout(() => {
                console.log('🔍 Checking if branches are loaded...');
                const branchesGrid = document.getElementById('dynamic-branches');
                const branchCards = branchesGrid ? branchesGrid.querySelectorAll('.branch-card') : [];
                console.log(`📊 Found ${branchCards.length} branch cards in DOM`);

                if (branchCards.length === 0) {
                    console.log('🔄 No branches found, reloading content...');
                    loadContentDirectly();
                }
            }, 2000);

            // Initialize contact links after other managers load
            setTimeout(() => {
                initializeContactLinks();

                // Debug data loading after 3 seconds
                setTimeout(() => {
                    debugDataLoading();
                }, 3000);
            }, 2000);
        });
    </script>
</body>
</html>
